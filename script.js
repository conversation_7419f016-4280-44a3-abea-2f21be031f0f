/**
 * Main application script
 */

class MapRankApp {
    constructor() {
        this.api = new SerproBotAPI();
        this.geocoding = new GeocodingService();
        this.map = null;
        this.projects = [];
        this.domainGroups = {};
        this.currentData = [];
        this.filters = {
            domain: '',
            projects: [], // Array of selected project IDs
            keyword: '',
            dateRange: '30',
            rankRange: 'all'
        };
        
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            this.showLoading(true);
            
            // Initialize map
            this.map = new MapManager('map');
            
            // Load initial data
            await this.loadProjects();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Load and display data
            await this.loadAndDisplayData();
            
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showError('Failed to load application. Please refresh the page.');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Load projects from API
     */
    async loadProjects() {
        try {
            this.projects = await this.api.getProjects();
            this.domainGroups = DataProcessor.groupProjectsByDomain(this.projects);
            this.populateDomainSelect();
        } catch (error) {
            console.error('Failed to load projects:', error);
            throw error;
        }
    }

    /**
     * Populate domain select dropdown
     */
    populateDomainSelect() {
        const select = document.getElementById('domain-select');
        select.innerHTML = '<option value="">All Domains</option>';

        Object.keys(this.domainGroups).sort().forEach(domain => {
            const option = document.createElement('option');
            option.value = domain;
            option.textContent = `${DataProcessor.extractDomain(domain)} (${this.domainGroups[domain].length} projects)`;
            select.appendChild(option);
        });
    }

    /**
     * Populate project checkboxes for selected domain
     */
    populateProjectList(domain) {
        const container = document.getElementById('project-list');

        if (!domain || !this.domainGroups[domain]) {
            container.innerHTML = '<p class="no-selection">Select a domain first</p>';
            return;
        }

        const projects = this.domainGroups[domain];
        container.innerHTML = '';

        projects.forEach(project => {
            const location = DataProcessor.extractLocation(project.name) || 'Unknown Location';

            const checkboxItem = document.createElement('div');
            checkboxItem.className = 'checkbox-item';

            checkboxItem.innerHTML = `
                <input type="checkbox" id="project-${project.id}" value="${project.id}">
                <label for="project-${project.id}">
                    ${project.name}
                    <span class="location-tag">${location}</span>
                </label>
            `;

            container.appendChild(checkboxItem);
        });

        // Add event listeners to checkboxes
        container.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateSelectedProjects();
            });
        });
    }

    /**
     * Update selected projects array based on checkboxes
     */
    updateSelectedProjects() {
        const checkboxes = document.querySelectorAll('#project-list input[type="checkbox"]:checked');
        this.filters.projects = Array.from(checkboxes).map(cb => cb.value);

        // Update keyword dropdown when project selection changes
        this.loadProjectKeywords();
    }

    /**
     * Load keywords for selected projects
     */
    async loadProjectKeywords() {
        const keywordSelect = document.getElementById('keyword-select');

        if (this.filters.projects.length === 0) {
            keywordSelect.innerHTML = '<option value="">Select projects first</option>';
            return;
        }

        try {
            // Load all selected projects
            const projectsWithKeywords = await this.api.getProjectsWithKeywords(this.filters.projects);

            // Get unique keywords across all selected projects
            const uniqueKeywords = DataProcessor.getUniqueKeywords(projectsWithKeywords);

            this.populateKeywordSelect(uniqueKeywords);
        } catch (error) {
            console.error('Failed to load project keywords:', error);
        }
    }

    /**
     * Populate keyword select dropdown with unique keywords
     */
    populateKeywordSelect(uniqueKeywords) {
        const select = document.getElementById('keyword-select');
        select.innerHTML = '<option value="">All Keywords</option>';

        uniqueKeywords.forEach(keywordData => {
            const option = document.createElement('option');
            option.value = keywordData.keyword;
            option.textContent = `${keywordData.keyword} (${keywordData.projects.length} projects)`;
            select.appendChild(option);
        });
    }

    /**
     * Load and display data on map
     */
    async loadAndDisplayData() {
        try {
            this.showLoading(true);

            // Get selected projects (or sample if none selected)
            const projectsToLoad = this.filters.projects.length > 0 ?
                this.filters.projects :
                this.projects.slice(0, 20).map(p => p.id); // Limit to first 20 for performance

            // Load project details with keywords
            const projectsWithKeywords = await this.api.getProjectsWithKeywords(projectsToLoad);

            // Extract all keywords with their project info
            const allKeywords = [];
            projectsWithKeywords.forEach(project => {
                if (project.keywords) {
                    project.keywords.forEach(keyword => {
                        allKeywords.push({
                            project: project,
                            keyword: keyword
                        });
                    });
                }
            });

            // Filter keywords based on current filters
            let filteredKeywords = this.applyFilters(allKeywords);

            // Get unique locations for geocoding
            const locations = [...new Set(
                filteredKeywords
                    .map(item => DataProcessor.extractLocation(item.project.name))
                    .filter(Boolean)
            )];

            // Geocode locations
            const coordinates = await this.geocoding.getMultipleCoordinates(locations);

            // Combine data with coordinates
            this.currentData = filteredKeywords
                .map(item => {
                    const location = DataProcessor.extractLocation(item.project.name);
                    const coords = coordinates[location];

                    if (coords) {
                        return {
                            ...item,
                            location: location,
                            coordinates: coords
                        };
                    }
                    return null;
                })
                .filter(Boolean);

            // Display on map
            this.map.addMarkers(this.currentData);

            // Update statistics
            this.updateStatistics();

        } catch (error) {
            console.error('Failed to load and display data:', error);
            this.showError('Failed to load data. Please try again.');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Apply current filters to keyword data
     */
    applyFilters(keywordData) {
        let filtered = [...keywordData];

        // Filter by keyword if selected
        if (this.filters.keyword) {
            filtered = filtered.filter(item =>
                item.keyword.keyword.toLowerCase() === this.filters.keyword.toLowerCase()
            );
        }

        // Filter by date range
        if (this.filters.dateRange) {
            const keywords = DataProcessor.filterByDateRange(
                filtered.map(item => item.keyword),
                parseInt(this.filters.dateRange)
            );
            filtered = filtered.filter(item =>
                keywords.some(k => k.id === item.keyword.id)
            );
        }

        // Filter by ranking range
        if (this.filters.rankRange !== 'all') {
            const keywords = DataProcessor.filterByRankingRange(
                filtered.map(item => item.keyword),
                this.filters.rankRange
            );
            filtered = filtered.filter(item =>
                keywords.some(k => k.id === item.keyword.id)
            );
        }

        return filtered;
    }

    /**
     * Update statistics display
     */
    updateStatistics() {
        const projects = [...new Set(this.currentData.map(item => item.project.id))];
        const keywords = this.currentData.map(item => item.keyword);
        
        const stats = DataProcessor.calculateStats(projects, keywords);
        
        document.getElementById('total-projects').textContent = stats.totalProjects;
        document.getElementById('total-keywords').textContent = stats.totalKeywords;
        document.getElementById('avg-position').textContent = stats.avgPosition;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Domain selection change
        document.getElementById('domain-select').addEventListener('change', (e) => {
            this.filters.domain = e.target.value;
            this.populateProjectList(e.target.value);
            this.filters.projects = []; // Reset project selection
            this.loadProjectKeywords(); // Reset keywords
        });

        // Select all projects button
        document.getElementById('select-all-projects').addEventListener('click', () => {
            const checkboxes = document.querySelectorAll('#project-list input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = true);
            this.updateSelectedProjects();
        });

        // Deselect all projects button
        document.getElementById('deselect-all-projects').addEventListener('click', () => {
            const checkboxes = document.querySelectorAll('#project-list input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            this.updateSelectedProjects();
        });

        // Keyword selection change
        document.getElementById('keyword-select').addEventListener('change', (e) => {
            this.filters.keyword = e.target.value;
        });

        // Date range change
        document.getElementById('date-range').addEventListener('change', (e) => {
            this.filters.dateRange = e.target.value;
        });

        // Rank filter change
        document.getElementById('rank-filter').addEventListener('change', (e) => {
            this.filters.rankRange = e.target.value;
        });

        // Apply filters button
        document.getElementById('apply-filters').addEventListener('click', () => {
            this.loadAndDisplayData();
        });

        // Reset filters button
        document.getElementById('reset-filters').addEventListener('click', () => {
            this.resetFilters();
        });

        // Modal close
        document.querySelector('.close').addEventListener('click', () => {
            this.closeModal();
        });

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('detail-modal');
            if (e.target === modal) {
                this.closeModal();
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (this.map) {
                this.map.resize();
            }
        });
    }

    /**
     * Reset all filters
     */
    resetFilters() {
        this.filters = {
            domain: '',
            projects: [],
            keyword: '',
            dateRange: '30',
            rankRange: 'all'
        };

        // Reset form elements
        document.getElementById('domain-select').value = '';
        document.getElementById('project-list').innerHTML = '<p class="no-selection">Select a domain first</p>';
        document.getElementById('keyword-select').innerHTML = '<option value="">Select projects first</option>';
        document.getElementById('date-range').value = '30';
        document.getElementById('rank-filter').value = 'all';

        // Reload data
        this.loadAndDisplayData();
    }

    /**
     * Show/hide loading overlay
     */
    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.remove('hidden');
        } else {
            overlay.classList.add('hidden');
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        // Simple alert for now - could be enhanced with a proper notification system
        alert(message);
    }

    /**
     * Show keyword details in modal
     */
    async showKeywordDetails(keywordId, projectId) {
        try {
            this.showLoading(true);

            const keyword = await this.api.getKeyword(keywordId);
            const project = this.projects.find(p => p.id.toString() === projectId.toString());

            this.displayKeywordModal(keyword, project);

        } catch (error) {
            console.error('Failed to load keyword details:', error);
            this.showError('Failed to load keyword details.');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Display keyword details in modal
     */
    displayKeywordModal(keyword, project) {
        const modal = document.getElementById('detail-modal');
        const title = document.getElementById('modal-title');
        const body = document.getElementById('modal-body');

        title.textContent = `${keyword.keyword} - ${project ? project.name : 'Unknown Project'}`;

        // Create chart data for ranking history
        const chartData = this.prepareChartData(keyword.check_data || []);

        body.innerHTML = `
            <div class="keyword-details">
                <div class="detail-section">
                    <h3>Current Status</h3>
                    <p><strong>Current Position:</strong> #${keyword.current_position}</p>
                    <p><strong>Best Position:</strong> #${keyword.best_position || 'N/A'}</p>
                    <p><strong>Latest Change:</strong> ${keyword.latest_change || 0}</p>
                    <p><strong>Search Volume:</strong> ${keyword.search_volume || 'N/A'}</p>
                    <p><strong>Last Checked:</strong> ${keyword.last_checked ? new Date(keyword.last_checked).toLocaleString() : 'Unknown'}</p>
                </div>

                <div class="detail-section">
                    <h3>Ranking History</h3>
                    <canvas id="ranking-chart" width="400" height="200"></canvas>
                </div>

                <div class="detail-section">
                    <h3>Recent Check Data</h3>
                    <div class="check-data-list">
                        ${this.renderCheckData(keyword.check_data || [])}
                    </div>
                </div>
            </div>
        `;

        // Show modal
        modal.style.display = 'block';

        // Create chart
        this.createRankingChart(chartData);
    }

    /**
     * Prepare chart data from check data
     */
    prepareChartData(checkData) {
        const sortedData = checkData
            .slice(-30) // Last 30 data points
            .sort((a, b) => new Date(a.created) - new Date(b.created));

        return {
            labels: sortedData.map(item => new Date(item.created).toLocaleDateString()),
            positions: sortedData.map(item => item.position)
        };
    }

    /**
     * Create ranking chart
     */
    createRankingChart(data) {
        const ctx = document.getElementById('ranking-chart').getContext('2d');

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Ranking Position',
                    data: data.positions,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        reverse: true, // Lower numbers (better rankings) at top
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: 'Position'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Ranking Position Over Time'
                    }
                }
            }
        });
    }

    /**
     * Render check data list
     */
    renderCheckData(checkData) {
        const recentData = checkData
            .slice(-10) // Last 10 checks
            .sort((a, b) => new Date(b.created) - new Date(a.created));

        if (recentData.length === 0) {
            return '<p>No recent check data available.</p>';
        }

        return recentData.map(item => `
            <div class="check-item">
                <span class="check-date">${new Date(item.created).toLocaleString()}</span>
                <span class="check-position">#${item.position}</span>
                <span class="check-url">${item.found_serp || 'Not found'}</span>
            </div>
        `).join('');
    }

    /**
     * Close modal
     */
    closeModal() {
        const modal = document.getElementById('detail-modal');
        modal.style.display = 'none';
    }
}

// Global function for keyword details (called from map popups)
window.showKeywordDetails = async (keywordId, projectId) => {
    if (window.app) {
        await window.app.showKeywordDetails(keywordId, projectId);
    }
};

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new MapRankApp();
});
