/**
 * Main application script
 */

class MapRankApp {
    constructor() {
        this.api = new SerproBotAPI();
        this.geocoding = new GeocodingService();
        this.map = null;
        this.projects = [];
        this.currentData = [];
        this.filters = {
            project: '',
            keyword: '',
            dateRange: '30',
            rankRange: 'all'
        };
        
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            this.showLoading(true);
            
            // Initialize map
            this.map = new MapManager('map');
            
            // Load initial data
            await this.loadProjects();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Load and display data
            await this.loadAndDisplayData();
            
        } catch (error) {
            console.error('Failed to initialize app:', error);
            this.showError('Failed to load application. Please refresh the page.');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Load projects from API
     */
    async loadProjects() {
        try {
            this.projects = await this.api.getProjects();
            this.populateProjectSelect();
        } catch (error) {
            console.error('Failed to load projects:', error);
            throw error;
        }
    }

    /**
     * Populate project select dropdown
     */
    populateProjectSelect() {
        const select = document.getElementById('project-select');
        select.innerHTML = '<option value="">All Projects</option>';
        
        this.projects.forEach(project => {
            const option = document.createElement('option');
            option.value = project.id;
            option.textContent = project.name;
            select.appendChild(option);
        });
    }

    /**
     * Load project details and populate keyword select
     */
    async loadProjectKeywords(projectId) {
        if (!projectId) {
            const keywordSelect = document.getElementById('keyword-select');
            keywordSelect.innerHTML = '<option value="">Select a project first</option>';
            return;
        }

        try {
            const project = await this.api.getProject(projectId);
            this.populateKeywordSelect(project.keywords || []);
        } catch (error) {
            console.error('Failed to load project keywords:', error);
        }
    }

    /**
     * Populate keyword select dropdown
     */
    populateKeywordSelect(keywords) {
        const select = document.getElementById('keyword-select');
        select.innerHTML = '<option value="">All Keywords</option>';
        
        keywords.forEach(keyword => {
            const option = document.createElement('option');
            option.value = keyword.id;
            option.textContent = keyword.keyword;
            select.appendChild(option);
        });
    }

    /**
     * Load and display data on map
     */
    async loadAndDisplayData() {
        try {
            this.showLoading(true);
            
            // Get selected projects (or all if none selected)
            const selectedProjectId = this.filters.project;
            const projectsToLoad = selectedProjectId ? 
                [selectedProjectId] : 
                this.projects.slice(0, 20).map(p => p.id); // Limit to first 20 for performance

            // Load project details with keywords
            const projectsWithKeywords = await this.api.getProjectsWithKeywords(projectsToLoad);
            
            // Extract all keywords with their project info
            const allKeywords = [];
            projectsWithKeywords.forEach(project => {
                if (project.keywords) {
                    project.keywords.forEach(keyword => {
                        allKeywords.push({
                            project: project,
                            keyword: keyword
                        });
                    });
                }
            });

            // Filter keywords based on current filters
            let filteredKeywords = this.applyFilters(allKeywords);

            // Get unique locations for geocoding
            const locations = [...new Set(
                filteredKeywords
                    .map(item => DataProcessor.extractLocation(item.project.name))
                    .filter(Boolean)
            )];

            // Geocode locations
            const coordinates = await this.geocoding.getMultipleCoordinates(locations);

            // Combine data with coordinates
            this.currentData = filteredKeywords
                .map(item => {
                    const location = DataProcessor.extractLocation(item.project.name);
                    const coords = coordinates[location];
                    
                    if (coords) {
                        return {
                            ...item,
                            location: location,
                            coordinates: coords
                        };
                    }
                    return null;
                })
                .filter(Boolean);

            // Display on map
            this.map.addMarkers(this.currentData);
            
            // Update statistics
            this.updateStatistics();
            
        } catch (error) {
            console.error('Failed to load and display data:', error);
            this.showError('Failed to load data. Please try again.');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Apply current filters to keyword data
     */
    applyFilters(keywordData) {
        let filtered = [...keywordData];

        // Filter by keyword if selected
        if (this.filters.keyword) {
            filtered = filtered.filter(item => 
                item.keyword.id.toString() === this.filters.keyword
            );
        }

        // Filter by date range
        if (this.filters.dateRange) {
            const keywords = DataProcessor.filterByDateRange(
                filtered.map(item => item.keyword),
                parseInt(this.filters.dateRange)
            );
            filtered = filtered.filter(item => 
                keywords.some(k => k.id === item.keyword.id)
            );
        }

        // Filter by ranking range
        if (this.filters.rankRange !== 'all') {
            const keywords = DataProcessor.filterByRankingRange(
                filtered.map(item => item.keyword),
                this.filters.rankRange
            );
            filtered = filtered.filter(item => 
                keywords.some(k => k.id === item.keyword.id)
            );
        }

        return filtered;
    }

    /**
     * Update statistics display
     */
    updateStatistics() {
        const projects = [...new Set(this.currentData.map(item => item.project.id))];
        const keywords = this.currentData.map(item => item.keyword);
        
        const stats = DataProcessor.calculateStats(projects, keywords);
        
        document.getElementById('total-projects').textContent = stats.totalProjects;
        document.getElementById('total-keywords').textContent = stats.totalKeywords;
        document.getElementById('avg-position').textContent = stats.avgPosition;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Project selection change
        document.getElementById('project-select').addEventListener('change', async (e) => {
            this.filters.project = e.target.value;
            await this.loadProjectKeywords(e.target.value);
        });

        // Keyword selection change
        document.getElementById('keyword-select').addEventListener('change', (e) => {
            this.filters.keyword = e.target.value;
        });

        // Date range change
        document.getElementById('date-range').addEventListener('change', (e) => {
            this.filters.dateRange = e.target.value;
        });

        // Rank filter change
        document.getElementById('rank-filter').addEventListener('change', (e) => {
            this.filters.rankRange = e.target.value;
        });

        // Apply filters button
        document.getElementById('apply-filters').addEventListener('click', () => {
            this.loadAndDisplayData();
        });

        // Reset filters button
        document.getElementById('reset-filters').addEventListener('click', () => {
            this.resetFilters();
        });

        // Modal close
        document.querySelector('.close').addEventListener('click', () => {
            this.closeModal();
        });

        // Close modal when clicking outside
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('detail-modal');
            if (e.target === modal) {
                this.closeModal();
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            if (this.map) {
                this.map.resize();
            }
        });
    }

    /**
     * Reset all filters
     */
    resetFilters() {
        this.filters = {
            project: '',
            keyword: '',
            dateRange: '30',
            rankRange: 'all'
        };

        // Reset form elements
        document.getElementById('project-select').value = '';
        document.getElementById('keyword-select').innerHTML = '<option value="">Select a project first</option>';
        document.getElementById('date-range').value = '30';
        document.getElementById('rank-filter').value = 'all';

        // Reload data
        this.loadAndDisplayData();
    }

    /**
     * Show/hide loading overlay
     */
    showLoading(show) {
        const overlay = document.getElementById('loading-overlay');
        if (show) {
            overlay.classList.remove('hidden');
        } else {
            overlay.classList.add('hidden');
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        // Simple alert for now - could be enhanced with a proper notification system
        alert(message);
    }

    /**
     * Show keyword details in modal
     */
    async showKeywordDetails(keywordId, projectId) {
        try {
            this.showLoading(true);

            const keyword = await this.api.getKeyword(keywordId);
            const project = this.projects.find(p => p.id.toString() === projectId.toString());

            this.displayKeywordModal(keyword, project);

        } catch (error) {
            console.error('Failed to load keyword details:', error);
            this.showError('Failed to load keyword details.');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * Display keyword details in modal
     */
    displayKeywordModal(keyword, project) {
        const modal = document.getElementById('detail-modal');
        const title = document.getElementById('modal-title');
        const body = document.getElementById('modal-body');

        title.textContent = `${keyword.keyword} - ${project ? project.name : 'Unknown Project'}`;

        // Create chart data for ranking history
        const chartData = this.prepareChartData(keyword.check_data || []);

        body.innerHTML = `
            <div class="keyword-details">
                <div class="detail-section">
                    <h3>Current Status</h3>
                    <p><strong>Current Position:</strong> #${keyword.current_position}</p>
                    <p><strong>Best Position:</strong> #${keyword.best_position || 'N/A'}</p>
                    <p><strong>Latest Change:</strong> ${keyword.latest_change || 0}</p>
                    <p><strong>Search Volume:</strong> ${keyword.search_volume || 'N/A'}</p>
                    <p><strong>Last Checked:</strong> ${keyword.last_checked ? new Date(keyword.last_checked).toLocaleString() : 'Unknown'}</p>
                </div>

                <div class="detail-section">
                    <h3>Ranking History</h3>
                    <canvas id="ranking-chart" width="400" height="200"></canvas>
                </div>

                <div class="detail-section">
                    <h3>Recent Check Data</h3>
                    <div class="check-data-list">
                        ${this.renderCheckData(keyword.check_data || [])}
                    </div>
                </div>
            </div>
        `;

        // Show modal
        modal.style.display = 'block';

        // Create chart
        this.createRankingChart(chartData);
    }

    /**
     * Prepare chart data from check data
     */
    prepareChartData(checkData) {
        const sortedData = checkData
            .slice(-30) // Last 30 data points
            .sort((a, b) => new Date(a.created) - new Date(b.created));

        return {
            labels: sortedData.map(item => new Date(item.created).toLocaleDateString()),
            positions: sortedData.map(item => item.position)
        };
    }

    /**
     * Create ranking chart
     */
    createRankingChart(data) {
        const ctx = document.getElementById('ranking-chart').getContext('2d');

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [{
                    label: 'Ranking Position',
                    data: data.positions,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        reverse: true, // Lower numbers (better rankings) at top
                        beginAtZero: false,
                        title: {
                            display: true,
                            text: 'Position'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Ranking Position Over Time'
                    }
                }
            }
        });
    }

    /**
     * Render check data list
     */
    renderCheckData(checkData) {
        const recentData = checkData
            .slice(-10) // Last 10 checks
            .sort((a, b) => new Date(b.created) - new Date(a.created));

        if (recentData.length === 0) {
            return '<p>No recent check data available.</p>';
        }

        return recentData.map(item => `
            <div class="check-item">
                <span class="check-date">${new Date(item.created).toLocaleString()}</span>
                <span class="check-position">#${item.position}</span>
                <span class="check-url">${item.found_serp || 'Not found'}</span>
            </div>
        `).join('');
    }

    /**
     * Close modal
     */
    closeModal() {
        const modal = document.getElementById('detail-modal');
        modal.style.display = 'none';
    }
}

// Global function for keyword details (called from map popups)
window.showKeywordDetails = async (keywordId, projectId) => {
    if (window.app) {
        await window.app.showKeywordDetails(keywordId, projectId);
    }
};

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new MapRankApp();
});
