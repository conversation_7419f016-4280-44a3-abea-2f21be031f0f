/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

/* App container */
.app-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.header p {
    opacity: 0.9;
    font-size: 1.1rem;
}

/* Main content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 320px;
    background: white;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.controls {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.controls h3 {
    margin-bottom: 1rem;
    color: #555;
}

.filter-group {
    margin-bottom: 1rem;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #666;
}

.filter-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    background: white;
}

.filter-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* Buttons */
.btn-primary, .btn-secondary {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #e9ecef;
}

.btn-small {
    padding: 0.5rem 0.75rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s;
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.btn-small:hover {
    background: #e9ecef;
}

/* Checkbox list styles */
.checkbox-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0.5rem;
    background: white;
    margin-bottom: 0.5rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.25rem;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.checkbox-item:hover {
    background-color: #f8f9fa;
}

.checkbox-item input[type="checkbox"] {
    margin-right: 0.75rem;
    cursor: pointer;
}

.checkbox-item label {
    cursor: pointer;
    font-size: 0.9rem;
    line-height: 1.3;
    margin-bottom: 0;
    flex: 1;
}

.checkbox-item .location-tag {
    background: #e9ecef;
    color: #666;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    margin-left: 0.5rem;
}

.no-selection {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 1rem;
    margin: 0;
}

.project-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

/* Legend */
.legend {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.legend h3 {
    margin-bottom: 1rem;
    color: #555;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.marker-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 0.75rem;
    border: 2px solid white;
    box-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.rank-1-3 { background-color: #28a745; }
.rank-4-10 { background-color: #ffc107; }
.rank-11-20 { background-color: #fd7e14; }
.rank-21-plus { background-color: #dc3545; }

/* Stats */
.stats {
    padding: 1.5rem;
}

.stats h3 {
    margin-bottom: 1rem;
    color: #555;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
}

/* Map container */
.map-container {
    flex: 1;
    position: relative;
}

#map {
    height: 100%;
    width: 100%;
}

/* Loading overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.hidden {
    display: none;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 8px;
    width: 80%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: #000;
}

/* Responsive design */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        max-height: 40vh;
    }
    
    .header {
        padding: 1rem;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
        padding: 1rem;
    }
}

/* Custom Leaflet popup styles */
.leaflet-popup-content-wrapper {
    border-radius: 8px;
}

.popup-content {
    min-width: 200px;
}

.popup-content h4 {
    margin-bottom: 0.5rem;
    color: #333;
}

.popup-content p {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.popup-content .keyword {
    font-weight: bold;
    color: #667eea;
}

.popup-content .position {
    font-weight: bold;
}

.popup-content .position.good {
    color: #28a745;
}

.popup-content .position.ok {
    color: #ffc107;
}

.popup-content .position.poor {
    color: #dc3545;
}

.popup-content .btn-details {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 0.5rem;
    font-size: 0.8rem;
}

.popup-content .btn-details:hover {
    background: #5a6fd8;
}

.popup-content .project-location {
    color: #666;
    font-size: 0.85rem;
    margin-bottom: 0.75rem;
}

.popup-content .project-name {
    color: #888;
    font-size: 0.8rem;
    font-style: italic;
    margin-top: 0.5rem;
    border-top: 1px solid #eee;
    padding-top: 0.5rem;
}

.popup-content .cluster-info {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.5rem;
    border-radius: 4px;
    margin-bottom: 0.75rem;
    font-size: 0.85rem;
    text-align: center;
}

/* Modal content styles */
.keyword-details {
    max-height: 60vh;
    overflow-y: auto;
}

.detail-section {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.detail-section:last-child {
    border-bottom: none;
}

.detail-section h3 {
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.2rem;
}

.detail-section p {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.detail-section strong {
    color: #555;
}

/* Check data list styles */
.check-data-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.check-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #f0f0f0;
    font-size: 0.9rem;
}

.check-item:last-child {
    border-bottom: none;
}

.check-date {
    color: #666;
    font-size: 0.8rem;
    min-width: 120px;
}

.check-position {
    font-weight: bold;
    color: #333;
    min-width: 40px;
    text-align: center;
}

.check-url {
    color: #667eea;
    font-size: 0.8rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
}

/* Chart container */
#ranking-chart {
    max-width: 100%;
    height: auto !important;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Error states */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
    border: 1px solid #f5c6cb;
}

/* Success states */
.success-message {
    background: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
    border: 1px solid #c3e6cb;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .check-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .check-date,
    .check-position,
    .check-url {
        min-width: auto;
        max-width: 100%;
    }

    .detail-section {
        margin-bottom: 1.5rem;
    }

    .keyword-details {
        max-height: 50vh;
    }
}
