/**
 * Map functionality using Leaflet.js
 */

class MapManager {
    constructor(containerId) {
        this.containerId = containerId;
        this.map = null;
        this.markers = [];
        this.markerClusterGroup = null;
        this.currentData = [];
        
        this.initializeMap();
    }

    /**
     * Initialize the Leaflet map
     */
    initializeMap() {
        // Initialize map centered on Texas
        this.map = L.map(this.containerId, {
            center: [31.9686, -99.9018], // Center of Texas
            zoom: 6,
            zoomControl: true,
            scrollWheelZoom: true
        });

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors',
            maxZoom: 18
        }).addTo(this.map);

        // Add zoom control
        this.map.zoomControl.setPosition('topright');

        // Add scale control
        L.control.scale().addTo(this.map);
    }

    /**
     * Clear all markers from the map
     */
    clearMarkers() {
        this.markers.forEach(marker => {
            this.map.removeLayer(marker);
        });
        this.markers = [];
    }

    /**
     * Create a custom marker icon based on ranking
     */
    createMarkerIcon(position, size = 'medium') {
        const color = DataProcessor.getRankingColor(position);
        const iconSize = size === 'small' ? [20, 20] : size === 'large' ? [40, 40] : [30, 30];
        
        return L.divIcon({
            className: 'custom-marker',
            html: `
                <div style="
                    width: ${iconSize[0]}px;
                    height: ${iconSize[1]}px;
                    background-color: ${color};
                    border: 3px solid white;
                    border-radius: 50%;
                    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-weight: bold;
                    font-size: ${size === 'small' ? '10px' : size === 'large' ? '16px' : '12px'};
                ">
                    ${position}
                </div>
            `,
            iconSize: iconSize,
            iconAnchor: [iconSize[0] / 2, iconSize[1] / 2],
            popupAnchor: [0, -iconSize[1] / 2]
        });
    }

    /**
     * Create popup content for a marker
     */
    createPopupContent(project, keyword) {
        const positionClass = keyword.current_position <= 3 ? 'good' :
                             keyword.current_position <= 10 ? 'ok' : 'poor';

        const lastChecked = keyword.last_checked ?
            new Date(keyword.last_checked).toLocaleDateString() : 'Unknown';

        const change = keyword.latest_change ?
            (keyword.latest_change > 0 ? `+${keyword.latest_change}` : keyword.latest_change) : '0';

        const changeIcon = keyword.latest_change > 0 ? '📈' :
                          keyword.latest_change < 0 ? '📉' : '➡️';

        const domain = DataProcessor.extractDomain(project.url);
        const location = DataProcessor.extractLocation(project.name) || 'Unknown Location';

        return `
            <div class="popup-content">
                <h4>${domain}</h4>
                <p class="project-location">📍 ${location}</p>
                <p><span class="keyword">${keyword.keyword}</span></p>
                <p>Position: <span class="position ${positionClass}">#${keyword.current_position}</span></p>
                <p>Change: ${changeIcon} ${change}</p>
                <p>Best Position: #${keyword.best_position || 'N/A'}</p>
                <p>Last Checked: ${lastChecked}</p>
                <p>Search Volume: ${keyword.search_volume || 'N/A'}</p>
                <p class="project-name">${project.name}</p>
                <button class="btn-details" onclick="window.showKeywordDetails('${keyword.id}', '${project.id}')">
                    View Details
                </button>
            </div>
        `;
    }

    /**
     * Add markers to the map
     */
    addMarkers(data) {
        this.clearMarkers();
        this.currentData = data;

        // Group data by location to handle multiple projects in same location
        const locationGroups = this.groupByLocation(data);

        Object.entries(locationGroups).forEach(([locationKey, items]) => {
            if (items.length === 1) {
                // Single marker for this location
                const item = items[0];
                this.addSingleMarker(item);
            } else {
                // Multiple projects in same location - create clustered markers with slight offset
                this.addClusteredMarkers(items);
            }
        });

        // Fit map to show all markers if there are any
        if (this.markers.length > 0) {
            const group = new L.featureGroup(this.markers);
            this.map.fitBounds(group.getBounds().pad(0.1));
        }
    }

    /**
     * Group data by location
     */
    groupByLocation(data) {
        const groups = {};

        data.forEach(item => {
            if (item.coordinates && item.project && item.keyword) {
                const key = `${item.coordinates.lat.toFixed(4)},${item.coordinates.lng.toFixed(4)}`;
                if (!groups[key]) {
                    groups[key] = [];
                }
                groups[key].push(item);
            }
        });

        return groups;
    }

    /**
     * Add a single marker
     */
    addSingleMarker(item) {
        const marker = L.marker(
            [item.coordinates.lat, item.coordinates.lng],
            { icon: this.createMarkerIcon(item.keyword.current_position) }
        );

        marker.bindPopup(this.createPopupContent(item.project, item.keyword));

        // Add click event for additional functionality
        marker.on('click', () => {
            this.onMarkerClick(item);
        });

        marker.addTo(this.map);
        this.markers.push(marker);
    }

    /**
     * Add clustered markers with slight offset
     */
    addClusteredMarkers(items) {
        const baseCoords = items[0].coordinates;
        const offsetIncrement = 0.001; // Small offset to separate markers

        items.forEach((item, index) => {
            // Calculate slight offset for each marker
            const offsetLat = baseCoords.lat + (Math.cos(index * 2 * Math.PI / items.length) * offsetIncrement);
            const offsetLng = baseCoords.lng + (Math.sin(index * 2 * Math.PI / items.length) * offsetIncrement);

            const marker = L.marker(
                [offsetLat, offsetLng],
                { icon: this.createMarkerIcon(item.keyword.current_position, 'small') }
            );

            // Create popup content that shows this is part of a cluster
            const popupContent = this.createClusteredPopupContent(item, items.length);
            marker.bindPopup(popupContent);

            marker.on('click', () => {
                this.onMarkerClick(item);
            });

            marker.addTo(this.map);
            this.markers.push(marker);
        });
    }

    /**
     * Create popup content for clustered markers
     */
    createClusteredPopupContent(item, totalInCluster) {
        const baseContent = this.createPopupContent(item.project, item.keyword);

        // Add cluster information
        const clusterInfo = `<p class="cluster-info">📍 ${totalInCluster} projects in this area</p>`;

        return baseContent.replace('<div class="popup-content">', `<div class="popup-content">${clusterInfo}`);
    }

    /**
     * Handle marker click events
     */
    onMarkerClick(item) {
        // This can be extended for additional functionality
        console.log('Marker clicked:', item);
    }



    /**
     * Add a heatmap layer (if needed in the future)
     */
    addHeatmap(data) {
        // This would require the Leaflet.heat plugin
        // Implementation can be added later if needed
    }

    /**
     * Get map bounds
     */
    getBounds() {
        return this.map.getBounds();
    }

    /**
     * Set map view
     */
    setView(lat, lng, zoom = 10) {
        this.map.setView([lat, lng], zoom);
    }

    /**
     * Resize map (useful when container size changes)
     */
    resize() {
        this.map.invalidateSize();
    }

    /**
     * Destroy the map
     */
    destroy() {
        if (this.map) {
            this.map.remove();
            this.map = null;
        }
    }
}

// Export for use in other files
window.MapManager = MapManager;
