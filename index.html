<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Map Rank - Keyword Ranking Visualization</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <h1>Map Rank</h1>
            <p>Visualize keyword rankings on an interactive map</p>
        </header>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Sidebar -->
            <aside class="sidebar">
                <div class="controls">
                    <h3>Filters</h3>
                    
                    <!-- Domain Filter -->
                    <div class="filter-group">
                        <label for="domain-select">Domain:</label>
                        <select id="domain-select">
                            <option value="">Loading domains...</option>
                        </select>
                    </div>

                    <!-- Project Filter (Multi-select) -->
                    <div class="filter-group">
                        <label for="project-list">Projects for Domain:</label>
                        <div id="project-list" class="checkbox-list">
                            <p class="no-selection">Select a domain first</p>
                        </div>
                        <div class="project-actions">
                            <button type="button" id="select-all-projects" class="btn-small">Select All</button>
                            <button type="button" id="deselect-all-projects" class="btn-small">Deselect All</button>
                        </div>
                    </div>

                    <!-- Keyword Filter -->
                    <div class="filter-group">
                        <label for="keyword-select">Keyword:</label>
                        <select id="keyword-select">
                            <option value="">Select projects first</option>
                        </select>
                    </div>

                    <!-- Date Range -->
                    <div class="filter-group">
                        <label for="date-range">Date Range:</label>
                        <select id="date-range">
                            <option value="7">Last 7 days</option>
                            <option value="30" selected>Last 30 days</option>
                            <option value="90">Last 90 days</option>
                            <option value="365">Last year</option>
                        </select>
                    </div>

                    <!-- Ranking Filter -->
                    <div class="filter-group">
                        <label for="rank-filter">Show Rankings:</label>
                        <select id="rank-filter">
                            <option value="all">All positions</option>
                            <option value="1-3">Top 3 (1-3)</option>
                            <option value="1-10">Top 10 (1-10)</option>
                            <option value="11-20">11-20</option>
                            <option value="21+">21+</option>
                        </select>
                    </div>

                    <button id="apply-filters" class="btn-primary">Apply Filters</button>
                    <button id="reset-filters" class="btn-secondary">Reset</button>
                </div>

                <!-- Legend -->
                <div class="legend">
                    <h3>Legend</h3>
                    <div class="legend-item">
                        <span class="marker-icon rank-1-3"></span>
                        <span>Rank 1-3</span>
                    </div>
                    <div class="legend-item">
                        <span class="marker-icon rank-4-10"></span>
                        <span>Rank 4-10</span>
                    </div>
                    <div class="legend-item">
                        <span class="marker-icon rank-11-20"></span>
                        <span>Rank 11-20</span>
                    </div>
                    <div class="legend-item">
                        <span class="marker-icon rank-21-plus"></span>
                        <span>Rank 21+</span>
                    </div>
                </div>

                <!-- Stats -->
                <div class="stats">
                    <h3>Statistics</h3>
                    <div class="stat-item">
                        <span class="stat-label">Total Projects:</span>
                        <span id="total-projects">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Total Keywords:</span>
                        <span id="total-keywords">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Avg. Position:</span>
                        <span id="avg-position">-</span>
                    </div>
                </div>
            </aside>

            <!-- Map Container -->
            <main class="map-container">
                <div id="map"></div>
                <div class="loading-overlay" id="loading-overlay">
                    <div class="spinner"></div>
                    <p>Loading data...</p>
                </div>
            </main>
        </div>

        <!-- Modal for detailed view -->
        <div id="detail-modal" class="modal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h2 id="modal-title">Keyword Details</h2>
                <div id="modal-body">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="api.js"></script>
    <script src="geocoding.js"></script>
    <script src="map.js"></script>
    <script src="script.js"></script>
</body>
</html>
