/**
 * Geocoding utilities for converting location names to coordinates
 */

class GeocodingService {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 24 * 60 * 60 * 1000; // 24 hours
        
        // Predefined coordinates for common Texas cities
        this.knownLocations = {
            'Dallas': { lat: 32.7767, lng: -96.7970 },
            'Houston': { lat: 29.7604, lng: -95.3698 },
            'Austin': { lat: 30.2672, lng: -97.7431 },
            'San Antonio': { lat: 29.4241, lng: -98.4936 },
            'Fort Worth': { lat: 32.7555, lng: -97.3308 },
            'El Paso': { lat: 31.7619, lng: -106.4850 },
            'Arlington': { lat: 32.7357, lng: -97.1081 },
            'Corpus Christi': { lat: 27.8006, lng: -97.3964 },
            'Plano': { lat: 33.0198, lng: -96.6989 },
            'Lubbock': { lat: 33.5779, lng: -101.8552 },
            'Laredo': { lat: 27.5306, lng: -99.4803 },
            '<PERSON>': { lat: 32.8140, lng: -96.9489 },
            '<PERSON>': { lat: 32.9126, lng: -96.6389 },
            'Amarillo': { lat: 35.2220, lng: -101.8313 },
            'Grand Prairie': { lat: 32.7460, lng: -96.9978 },
            'Brownsville': { lat: 25.9018, lng: -97.4975 },
            'McKinney': { lat: 33.1972, lng: -96.6397 },
            'Frisco': { lat: 33.1507, lng: -96.8236 },
            'Pasadena': { lat: 29.6911, lng: -95.2091 },
            'Killeen': { lat: 31.1171, lng: -97.7278 },
            'Mesquite': { lat: 32.7668, lng: -96.5992 },
            'McAllen': { lat: 26.2034, lng: -98.2300 },
            'Carrollton': { lat: 32.9537, lng: -96.8903 },
            'Midland': { lat: 31.9974, lng: -102.0779 },
            'Waco': { lat: 31.5494, lng: -97.1467 },
            'Round Rock': { lat: 30.5083, lng: -97.6789 },
            'Richardson': { lat: 32.9483, lng: -96.7299 },
            'Pearland': { lat: 29.5638, lng: -95.2861 },
            'College Station': { lat: 30.6280, lng: -96.3344 },
            'Odessa': { lat: 31.8457, lng: -102.3676 },
            'Beaumont': { lat: 30.0860, lng: -94.1018 },
            'Abilene': { lat: 32.4487, lng: -99.7331 },
            'Tyler': { lat: 32.3513, lng: -95.3011 },
            'Denton': { lat: 33.2148, lng: -97.1331 },
            'Lewisville': { lat: 33.0462, lng: -96.9942 },
            'Sugar Land': { lat: 29.6196, lng: -95.6349 },
            'Edinburg': { lat: 26.3017, lng: -98.1633 },
            'Mission': { lat: 26.2159, lng: -98.3253 },
            'Bryan': { lat: 30.6744, lng: -96.3700 },
            'Pharr': { lat: 26.1948, lng: -98.1836 },
            'Temple': { lat: 31.0982, lng: -97.3428 },
            'Missouri City': { lat: 29.6186, lng: -95.5377 },
            'Flower Mound': { lat: 33.0137, lng: -97.0969 },
            'Harlingen': { lat: 26.1906, lng: -97.6961 },
            'North Richland Hills': { lat: 32.8342, lng: -97.2289 },
            'Victoria': { lat: 28.8053, lng: -97.0036 },
            'Conroe': { lat: 30.3119, lng: -95.4560 },
            'New Braunfels': { lat: 29.7030, lng: -98.1245 },
            'Cedar Park': { lat: 30.5052, lng: -97.8203 },
            'Georgetown': { lat: 30.6327, lng: -97.6779 },
            'League City': { lat: 29.5075, lng: -95.0949 },
            'Longview': { lat: 32.5007, lng: -94.7405 },
            'Baytown': { lat: 29.7355, lng: -94.9774 },
            'Alvarado': { lat: 32.4065, lng: -97.2111 },
            'Pecos': { lat: 31.4210, lng: -103.4932 },
            
            // Arizona cities
            'Phoenix': { lat: 33.4484, lng: -112.0740 },
            'Mesa': { lat: 33.4152, lng: -111.8315 },
            'Tucson': { lat: 32.2226, lng: -110.9747 },
            'Chandler': { lat: 33.3062, lng: -111.8413 },
            'Glendale': { lat: 33.5387, lng: -112.1860 },
            'Scottsdale': { lat: 33.4942, lng: -111.9261 },
            'Gilbert': { lat: 33.3528, lng: -111.7890 },
            'Tempe': { lat: 33.4255, lng: -111.9400 },
            'Peoria': { lat: 33.5806, lng: -112.2374 },
            'Surprise': { lat: 33.6292, lng: -112.3679 },
            'Yuma': { lat: 32.6927, lng: -114.6277 },
            'Avondale': { lat: 33.4356, lng: -112.3496 },
            'Flagstaff': { lat: 35.1983, lng: -111.6513 },
            'Goodyear': { lat: 33.4355, lng: -112.3576 },
            'Buckeye': { lat: 33.3703, lng: -112.5838 },
            'Lake Havasu City': { lat: 34.4839, lng: -114.3225 },
            'Casa Grande': { lat: 32.8795, lng: -111.7574 },
            'Sierra Vista': { lat: 31.5455, lng: -110.3032 },
            'Maricopa': { lat: 33.0581, lng: -112.0476 },
            'Oro Valley': { lat: 32.3909, lng: -110.9665 },
            'Prescott': { lat: 34.5400, lng: -112.4685 },
            'Bullhead City': { lat: 35.1478, lng: -114.5683 },
            'Prescott Valley': { lat: 34.6100, lng: -112.3157 },
            'Fountain Hills': { lat: 33.6119, lng: -111.7312 },
            'Kingman': { lat: 35.1894, lng: -114.0530 },
            'Apache Junction': { lat: 33.4151, lng: -111.5496 },
            'Great Falls': { lat: 47.5053, lng: -111.3008 }
        };
    }

    /**
     * Get coordinates for a location
     */
    async getCoordinates(locationName) {
        if (!locationName) return null;

        const normalizedName = this.normalizeLocationName(locationName);
        
        // Check cache first
        const cacheKey = normalizedName.toLowerCase();
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.coordinates;
            }
        }

        // Check known locations
        if (this.knownLocations[normalizedName]) {
            const coords = this.knownLocations[normalizedName];
            this.cache.set(cacheKey, {
                coordinates: coords,
                timestamp: Date.now()
            });
            return coords;
        }

        // Try to geocode using Nominatim (OpenStreetMap)
        try {
            const coords = await this.geocodeWithNominatim(normalizedName);
            if (coords) {
                this.cache.set(cacheKey, {
                    coordinates: coords,
                    timestamp: Date.now()
                });
                return coords;
            }
        } catch (error) {
            console.warn(`Geocoding failed for ${locationName}:`, error);
        }

        return null;
    }

    /**
     * Normalize location name
     */
    normalizeLocationName(name) {
        return name
            .replace(/\s+(TX|Texas|AZ|Arizona|MT|Montana)$/i, '')
            .replace(/\s+/g, ' ')
            .trim();
    }

    /**
     * Geocode using Nominatim API
     */
    async geocodeWithNominatim(locationName) {
        const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(locationName)}&limit=1&countrycodes=us`;
        
        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            if (data && data.length > 0) {
                return {
                    lat: parseFloat(data[0].lat),
                    lng: parseFloat(data[0].lon)
                };
            }
        } catch (error) {
            console.error('Nominatim geocoding error:', error);
        }
        
        return null;
    }

    /**
     * Get coordinates for multiple locations
     */
    async getMultipleCoordinates(locations) {
        const results = {};
        
        for (const location of locations) {
            if (location) {
                const coords = await this.getCoordinates(location);
                if (coords) {
                    results[location] = coords;
                }
                // Add small delay to be respectful to the geocoding service
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
        
        return results;
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * Add custom location
     */
    addKnownLocation(name, lat, lng) {
        this.knownLocations[name] = { lat, lng };
    }
}

// Export for use in other files
window.GeocodingService = GeocodingService;
