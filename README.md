# Map Rank - Keyword Ranking Visualization

A web application that visualizes keyword rankings from SerproBot API on an interactive map, allowing users to see how their SEO keywords perform across different geographic locations.

## Features

- **Interactive Map**: View keyword rankings plotted on a map with color-coded markers
- **Multi-Project Support**: Select multiple projects for the same domain across different locations
- **Domain-Based Organization**: Projects are grouped by domain for easier management
- **Real-time Data**: Fetches live data from SerproBot API
- **Advanced Filtering**: Filter by domain, multiple projects, keywords, date range, and ranking position
- **Location Intelligence**: Automatic location detection and geocoding from project names
- **Detailed Analytics**: Click on markers to see detailed keyword performance
- **Ranking History**: View historical ranking data with interactive charts
- **Smart Clustering**: Multiple projects in the same location are intelligently clustered
- **Responsive Design**: Works on desktop and mobile devices

## Color Coding

- 🟢 **Green (Rank 1-3)**: Top performing keywords
- 🟡 **Yellow (Rank 4-10)**: Good performing keywords  
- 🟠 **Orange (Rank 11-20)**: Moderate performing keywords
- 🔴 **Red (Rank 21+)**: Keywords needing improvement

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- SerproBot API access

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd map-rank
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## Usage

1. **Select a Domain**: Choose from the dropdown to see all projects for that domain
2. **Choose Projects**: Select one or multiple projects using checkboxes (use "Select All" for convenience)
3. **Filter Keywords**: Optionally filter by specific keywords that appear across selected projects
4. **Set Date Range**: Filter data by time period (7 days, 30 days, 90 days, or 1 year)
5. **Filter by Ranking**: Show only keywords within specific ranking ranges
6. **Apply Filters**: Click "Apply Filters" to update the map
7. **View Details**: Click on map markers to see detailed keyword information
8. **Explore Locations**: See how the same domain performs across different geographic locations

## API Configuration

The application uses the SerproBot API with the following endpoints:

- `list_projects` - Get all projects
- `project` - Get project details with keywords
- `keyword` - Get detailed keyword data with history

The API key is currently hardcoded in `api.js`. For production use, consider using environment variables.

## Technology Stack

- **Frontend**: Vanilla JavaScript, HTML5, CSS3
- **Mapping**: Leaflet.js
- **Charts**: Chart.js
- **Build Tool**: Vite
- **Geocoding**: OpenStreetMap Nominatim API

## File Structure

```
map-rank/
├── index.html          # Main HTML file
├── style.css           # Application styles
├── script.js           # Main application logic
├── api.js              # SerproBot API integration
├── geocoding.js        # Location geocoding utilities
├── map.js              # Map functionality
├── package.json        # Project dependencies
└── README.md           # This file
```

## Features in Detail

### Multi-Project Domain Support
- Projects are automatically grouped by domain (website URL)
- Select multiple projects for the same domain to see performance across locations
- Compare how the same keywords perform in different geographic markets
- Checkbox interface with "Select All" and "Deselect All" options

### Location Detection
The application automatically extracts location information from project names using pattern matching for common formats like:
- "ProjectName - CityName Local"
- "ProjectName - CityName Organic"
- "CityName ProjectName"

### Smart Map Clustering
- When multiple projects exist in the same location, markers are intelligently clustered
- Small offset positioning prevents marker overlap
- Cluster information is displayed in popups
- Smaller markers are used for clustered locations

### Geocoding
Locations are converted to coordinates using:
1. **Predefined coordinates** for common US cities (faster)
2. **OpenStreetMap Nominatim API** for other locations (fallback)

### Data Caching
- API responses are cached for 5 minutes to improve performance
- Geocoding results are cached for 24 hours
- Cache can be cleared manually if needed

### Cross-Project Keyword Analysis
- Keywords that appear across multiple projects are automatically identified
- Dropdown shows keyword frequency across selected projects
- Compare performance of the same keyword in different locations

## Customization

### Adding New Locations
To add predefined coordinates for new cities, edit the `knownLocations` object in `geocoding.js`:

```javascript
'New City': { lat: 40.7128, lng: -74.0060 }
```

### Styling Markers
Marker colors and styles can be customized in the `DataProcessor.getRankingColor()` function in `api.js`.

### API Configuration
Update the API key and endpoints in `api.js` as needed.

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues or questions, please create an issue in the repository or contact the development team.
