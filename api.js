/**
 * SerproBot API Integration
 */

class SerproBotAPI {
    constructor() {
        this.baseURL = 'https://api.serprobot.com/v1/api.php';
        this.apiKey = '63d7be40bb4e41b7c57c75c67a01dfdd';
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    }

    /**
     * Make API request with caching
     */
    async makeRequest(action, params = {}) {
        const cacheKey = `${action}_${JSON.stringify(params)}`;
        
        // Check cache first
        if (this.cache.has(cacheKey)) {
            const cached = this.cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this.cacheTimeout) {
                return cached.data;
            }
        }

        const url = new URL(this.baseURL);
        url.searchParams.append('api_key', this.apiKey);
        url.searchParams.append('action', action);
        
        Object.entries(params).forEach(([key, value]) => {
            url.searchParams.append(key, value);
        });

        try {
            const response = await fetch(url.toString());
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            // Cache the result
            this.cache.set(cacheKey, {
                data: data,
                timestamp: Date.now()
            });
            
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    /**
     * Get all projects
     */
    async getProjects() {
        return await this.makeRequest('list_projects');
    }

    /**
     * Get project details with keywords
     */
    async getProject(projectId) {
        return await this.makeRequest('project', { project_id: projectId });
    }

    /**
     * Get keyword details with historical data
     */
    async getKeyword(keywordId) {
        return await this.makeRequest('keyword', { keyword_id: keywordId });
    }

    /**
     * Get multiple projects with their keywords
     */
    async getProjectsWithKeywords(projectIds = []) {
        const projects = [];
        
        for (const projectId of projectIds) {
            try {
                const project = await this.getProject(projectId);
                projects.push(project);
            } catch (error) {
                console.error(`Failed to fetch project ${projectId}:`, error);
            }
        }
        
        return projects;
    }

    /**
     * Get keyword historical data for multiple keywords
     */
    async getKeywordsHistory(keywordIds = []) {
        const keywords = [];
        
        for (const keywordId of keywordIds) {
            try {
                const keyword = await this.getKeyword(keywordId);
                keywords.push(keyword);
            } catch (error) {
                console.error(`Failed to fetch keyword ${keywordId}:`, error);
            }
        }
        
        return keywords;
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }
}

/**
 * Data processing utilities
 */
class DataProcessor {
    /**
     * Extract location information from project name
     */
    static extractLocation(projectName) {
        // Common patterns in project names
        const patterns = [
            /- ([A-Za-z\s]+) (Local|Organic|Mobile)/i,
            /([A-Za-z\s]+) - (Local|Organic|Mobile)/i,
            /([A-Za-z\s]+) (Local|Organic|Mobile)/i
        ];

        for (const pattern of patterns) {
            const match = projectName.match(pattern);
            if (match) {
                return match[1].trim();
            }
        }

        // Fallback: try to extract city names from common patterns
        const cityPatterns = [
            /(Dallas|Houston|Austin|San Antonio|Fort Worth|El Paso|Arlington|Corpus Christi|Plano|Lubbock|Laredo|Irving|Garland|Amarillo|Grand Prairie|Brownsville|McKinney|Frisco|Pasadena|Killeen|Mesquite|McAllen|Carrollton|Midland|Waco|Round Rock|Richardson|Pearland|College Station|Odessa|Beaumont|Abilene|Tyler|Denton|Lewisville|Sugar Land|Edinburg|Mission|Bryan|Pharr|Temple|Missouri City|Flower Mound|Harlingen|North Richland Hills|Victoria|Conroe|New Braunfels|Cedar Park|Georgetown|League City|Longview|Baytown|Alvarado|Pecos|Phoenix|Mesa|Tucson|Chandler|Glendale|Scottsdale|Gilbert|Tempe|Peoria|Surprise|Yuma|Avondale|Flagstaff|Goodyear|Buckeye|Lake Havasu City|Casa Grande|Sierra Vista|Maricopa|Oro Valley|Prescott|Bullhead City|Prescott Valley|Fountain Hills|Kingman|Apache Junction|Carefree|Cave Creek|Clarkdale|Cottonwood|Dewey-Humboldt|Eagar|Eloy|Florence|Fredonia|Gila Bend|Globe|Hayden|Holbrook|Huachuca City|Jerome|Kearny|Litchfield Park|Mammoth|Miami|Nogales|Page|Paradise Valley|Parker|Payson|Quartzsite|Safford|Sahuarita|Sedona|Show Low|Snowflake|Somerton|South Tucson|Star Valley|Superior|Taylor|Thatcher|Tolleson|Tombstone|Tusayan|Wellton|Wickenburg|Willcox|Williams|Winkelman|Winslow|Youngtown)/i
        ];

        for (const pattern of cityPatterns) {
            const match = projectName.match(pattern);
            if (match) {
                return match[1];
            }
        }

        return null;
    }

    /**
     * Get ranking color based on position
     */
    static getRankingColor(position) {
        if (position >= 1 && position <= 3) return '#28a745'; // Green
        if (position >= 4 && position <= 10) return '#ffc107'; // Yellow
        if (position >= 11 && position <= 20) return '#fd7e14'; // Orange
        return '#dc3545'; // Red
    }

    /**
     * Get ranking category
     */
    static getRankingCategory(position) {
        if (position >= 1 && position <= 3) return 'rank-1-3';
        if (position >= 4 && position <= 10) return 'rank-4-10';
        if (position >= 11 && position <= 20) return 'rank-11-20';
        return 'rank-21-plus';
    }

    /**
     * Filter keywords by date range
     */
    static filterByDateRange(keywords, days) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        
        return keywords.filter(keyword => {
            if (!keyword.last_checked) return false;
            const lastChecked = new Date(keyword.last_checked);
            return lastChecked >= cutoffDate;
        });
    }

    /**
     * Filter keywords by ranking range
     */
    static filterByRankingRange(keywords, range) {
        if (range === 'all') return keywords;
        
        return keywords.filter(keyword => {
            const position = keyword.current_position;
            if (!position) return false;
            
            switch (range) {
                case '1-3': return position >= 1 && position <= 3;
                case '1-10': return position >= 1 && position <= 10;
                case '11-20': return position >= 11 && position <= 20;
                case '21+': return position >= 21;
                default: return true;
            }
        });
    }

    /**
     * Calculate statistics
     */
    static calculateStats(projects, keywords) {
        const totalProjects = projects.length;
        const totalKeywords = keywords.length;
        const validPositions = keywords
            .map(k => k.current_position)
            .filter(p => p && p > 0);
        
        const avgPosition = validPositions.length > 0 
            ? (validPositions.reduce((sum, pos) => sum + pos, 0) / validPositions.length).toFixed(1)
            : 0;

        return {
            totalProjects,
            totalKeywords,
            avgPosition
        };
    }
}

// Export for use in other files
window.SerproBotAPI = SerproBotAPI;
window.DataProcessor = DataProcessor;
